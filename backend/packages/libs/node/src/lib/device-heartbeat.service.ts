import { Injectable, Inject, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { NodeRepository } from "./node.repository";
import { RedisService } from '@saito/redis';
import { Interval } from '@nestjs/schedule';
import {
  DeviceHeartbeatRequest,
  NewDeviceHeartbeatRequest
} from '@saito/models';

/**
 * 设备心跳服务 - 处理设备心跳和状态监控
 */
@Injectable()
export class DeviceHeartbeatService {
  private readonly logger = new Logger(DeviceHeartbeatService.name);
  private readonly HEARTBEAT_TTL = 120; // 心跳数据保存2分钟
  private readonly DEVICE_HEARTBEAT_PREFIX = 'device:heartbeat:';

  constructor(
    @Inject(NodeRepository)
    private readonly nodeRepository: NodeRepository,
    @Inject(RedisService)
    private readonly redisService: RedisService
  ) {}

  /**
   * Update device heartbeat
   */
  async updateDeviceHeartbeat(
    request: DeviceHeartbeatRequest
  ): Promise<void> {
    try {
      const deviceId = request.device_id;
      const redisKey = `${this.DEVICE_HEARTBEAT_PREFIX}${deviceId}`;

      // 将心跳数据存储到Redis
      const heartbeatData = {
        deviceId,
        status: request.status,
        timestamp: new Date().toISOString(),
        cpu_usage_percent: request.cpu_usage_percent?.toString() || '',
        ram_usage_percent: request.ram_usage_percent?.toString() || '',
        gpu_usage_percent: request.gpu_usage_percent?.toString() || '',
        gpu_temperature: request.gpu_temperature?.toString() || '',
        network_in_kbps: request.network_in_kbps?.toString() || '',
        network_out_kbps: request.network_out_kbps?.toString() || '',
        uptime_seconds: request.uptime_seconds?.toString() || '',
        model: request.model || ''
      };

      // 存储到Redis，设置2分钟过期
      await this.redisService.hmset(redisKey, heartbeatData);
      await this.redisService.expire(redisKey, this.HEARTBEAT_TTL);

      // 确保所有数值字段都是正确的类型
      const cpuUsagePercent = request.cpu_usage_percent || null;
      const ramUsagePercent = request.ram_usage_percent || null;
      const gpuUsagePercent = request.gpu_usage_percent || null;
      const gpuTemperature = request.gpu_temperature || null;
      const networkInKbps = request.network_in_kbps || null;
      const networkOutKbps = request.network_out_kbps || null;
      // 确保 uptime_seconds 是整数
      const uptimeSeconds = request.uptime_seconds ? Math.floor(request.uptime_seconds) : null;

      // 更新数据库中的设备状态和最后心跳时间
      await this.nodeRepository.updateDeviceHeartbeat(
        request.device_id,
        request.status,
        cpuUsagePercent,
        ramUsagePercent,
        gpuUsagePercent,
        gpuTemperature,
        networkInKbps,
        networkOutKbps,
        request.model || null,
        uptimeSeconds
      );
    } catch (error) {
      this.logger.error(`Failed to update device heartbeat: ${error}`);
      throw new HttpException('Failed to update device heartbeat', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update device heartbeat with new format
   */
  async updateDeviceHeartbeatNew(request: NewDeviceHeartbeatRequest): Promise<void> {
    try {
      // 获取设备信息 - 首先尝试通过code查找，如果失败则尝试通过device_id查找
      let device = null;

      // 如果有code，通过code查找
      if (request.code) {
        device = await this.nodeRepository.getDeviceByCode(request.code);
      }

      // 如果通过code没找到，且有device_id，则通过device_id查找
      if (!device && request.device_id) {
        device = await this.nodeRepository.getDeviceById(request.device_id);
      }

      // 如果设备不存在，但有code和device_id，尝试自动注册设备
      if (!device && request.code && request.device_id) {
        this.logger.log(`Device not found, attempting auto-registration for device ${request.device_id} with code ${request.code}`);

        try {
          // 构建设备注册请求
          const registerRequest = {
            code: request.code,
            device_id: request.device_id,
            gateway_address: request.gateway_url || 'unknown',
            reward_address: 'unknown', // 这个需要从连接任务中获取
            device_type: request.type || 'unknown',
            gpu_type: request.device_info?.gpu_model || null,
            ip: request.ip || null,
            local_models: null
          };

          // 调用设备注册服务
          const registrationResult = await this.nodeRepository.registerDevice(
            registerRequest.code,
            registerRequest.device_id,
            registerRequest.gateway_address,
            registerRequest.reward_address,
            registerRequest.device_type,
            registerRequest.gpu_type,
            registerRequest.ip,
            registerRequest.local_models
          );

          this.logger.log(`Device ${request.device_id} auto-registered successfully`);

          // 重新获取设备信息
          device = await this.nodeRepository.getDeviceById(request.device_id);
        } catch (registerError) {
          this.logger.error(`Failed to auto-register device ${request.device_id}: ${registerError}`);
          throw new HttpException('Device not found and auto-registration failed', HttpStatus.NOT_FOUND);
        }
      }

      if (!device) {
        throw new HttpException('Device not found', HttpStatus.NOT_FOUND);
      }

      // 记录当前状态
      this.logger.log(`Device ${device.id} current status: ${device.status}`);

      // 构建标准心跳请求
      const heartbeatRequest: DeviceHeartbeatRequest = {
        device_id: device.id,
        status: 'connected',
        cpu_usage_percent: typeof request.cpu_usage === 'number' ? request.cpu_usage : undefined,
        ram_usage_percent: typeof request.memory_usage === 'number' ? request.memory_usage : undefined,
        gpu_usage_percent: typeof request.gpu_usage === 'number' ? request.gpu_usage : undefined,
        gpu_temperature: 30, // 默认GPU温度，可以根据实际情况调整
        network_in_kbps: 0.1, // 默认网络入站流量
        network_out_kbps: 0.1, // 默认网络出站流量
        // 确保 uptime_seconds 是整数
        uptime_seconds: request.timestamp ? Math.floor(parseFloat(request.timestamp)) : undefined,
        model: request.model
      };

      // 调用标准心跳更新方法
      await this.updateDeviceHeartbeat(heartbeatRequest);

      // 如果提供了设备信息，更新设备信息
      if (request.device_info) {
        // 确保 BIGINT 字段的值是整数，并进行单位转换（如果需要）
        // 假设输入的内存和磁盘大小是以GB为单位，转换为MB
        const ramTotal = request.device_info.ram_total ? Math.floor(request.device_info.ram_total * 1000) : null;
        const gpuMemory = request.device_info.gpu_memory ? Math.floor(request.device_info.gpu_memory * 1000) : null;
        const diskTotal = request.device_info.disk_total ? Math.floor(request.device_info.disk_total * 1000) : null;

        // 计算可用内存（假设使用率已知）
        const ramAvailable = (ramTotal && heartbeatRequest.ram_usage_percent)
          ? Math.floor(ramTotal * (1 - heartbeatRequest.ram_usage_percent / 100))
          : null;

        // 计算可用磁盘空间（假设为总空间的94%，可根据实际情况调整）
        const diskAvailable = diskTotal ? Math.floor(diskTotal * 0.94) : null;

        // 更新设备信息，包括额外的字段
        await this.nodeRepository.updateDeviceDetailedInfo(
          device.id,
          request.device_info.cpu_model || null,
          request.device_info.cpu_cores || null,
          request.device_info.cpu_threads || null,
          ramTotal,
          ramAvailable,
          request.device_info.gpu_model || null,
          request.device_info.gpu_count || null,
          gpuMemory,
          heartbeatRequest.gpu_temperature || null,
          diskTotal,
          diskAvailable,
          request.device_info.os_info || null,
          request.type || device.device_type || null
        );
      }

      // 如果提供了IP地址，更新IP地址
      if (request.ip) {
        await this.nodeRepository.updateDeviceIpAddress(device.id, request.ip);
      }
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to update device heartbeat: ${error}`);
      throw new HttpException('Failed to update device heartbeat', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 检查设备状态，每5秒执行一次
   * 如果设备2分钟内没有心跳，则标记为断开连接
   */
  @Interval(5000)
  async checkDevicesStatus() {
    try {
      // 获取所有设备
      const devices = await this.nodeRepository.getAllDevices();

      for (const device of devices) {
        // 检查Redis中是否有心跳数据
        const redisKey = `${this.DEVICE_HEARTBEAT_PREFIX}${device.id}`;
        const exists = await this.redisService.exists(redisKey);

        // 如果设备当前状态是connected，但Redis中没有心跳数据，则标记为disconnected
        if (device.status === 'connected' && !exists) {
          this.logger.log(`Device ${device.id} has no heartbeat, marking as disconnected`);
          const result = await this.nodeRepository.updateDeviceStatus(device.id, 'disconnected');
          if (result) {
            this.logger.log(`Device status changed from ${result.fromStatus} to ${result.toStatus}`);
          }
        }
        // 如果设备当前状态是disconnected，但Redis中有心跳数据，则标记为connected
        else if (device.status === 'disconnected' && exists) {
          this.logger.log(`Device ${device.id} has heartbeat, marking as connected`);
          const result = await this.nodeRepository.updateDeviceStatus(device.id, 'connected');
          if (result) {
            this.logger.log(`Device status changed from ${result.fromStatus} to ${result.toStatus}`);
          }
        }
        // 如果设备当前状态是waiting，但Redis中有心跳数据，则标记为connected
        else if (device.status === 'waiting' && exists) {
          this.logger.log(`Device ${device.id} has heartbeat, marking as connected from waiting state`);
          const result = await this.nodeRepository.updateDeviceStatus(device.id, 'connected');
          if (result) {
            this.logger.log(`Device status changed from ${result.fromStatus} to ${result.toStatus}`);
          }
        }
      }
    } catch (error) {
      this.logger.error(`Failed to check devices status: ${error}`);
    }
  }
}
