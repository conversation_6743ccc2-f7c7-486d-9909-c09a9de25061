import { Injectable, Inject, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { NodeRepository } from "./node.repository";
import { DeviceService } from "./device.service";
import {
  CreateConnectTaskRequest,
  CreateConnectTaskResponse,
  DeviceListRequest,
  DeviceListResponse,
  DeviceConnectionRequest,
  DeviceConnectionResponse
} from '@saito/models';
import { customAlphabet } from 'nanoid';
import { Interval } from '@nestjs/schedule';

/**
 * 连接任务服务 - 处理设备连接任务相关操作
 */
@Injectable()
export class ConnectTaskService {
  private readonly logger = new Logger(ConnectTaskService.name);

  constructor(
    @Inject(NodeRepository)
    private readonly nodeRepository: NodeRepository,
    private readonly deviceService: DeviceService
  ) {}

  /**
   * Create a new connect task for a device
   */
  async createConnectTask(
    request: CreateConnectTaskRequest,
    userId: string,
    walletAddress: string
  ): Promise<CreateConnectTaskResponse> {
    try {
      // 确保 walletAddress 不为 null 或 undefined
      const safeWalletAddress = walletAddress || '';

      // Ensure the user exists before creating a connect task
      const userCreated = await this.deviceService.createUserIfNotExists(userId, safeWalletAddress);
      if (!userCreated) {
        throw new HttpException('Failed to create or verify user', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      // 不再预先创建设备，设备将在注册时使用DID创建

      // Generate a one-time code
      const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', 8);
      const oneTimeCode = nanoid();

      // Create a connect task without device_id (will be set during registration)
      const { taskId } = await this.nodeRepository.createConnectTask(
        request.task_name,
        userId,
        safeWalletAddress,
        safeWalletAddress, // Use the same address for reward by default
        request.signature,
        null, // device_id will be set during device registration
        oneTimeCode,
        null, // Gateway address will be set when the device connects
        request.device_type || null,
        request.gpu_type || null
      );

      return {
        task_id: taskId,
        one_time_code: oneTimeCode,
        gateway_address: process.env["GATEWAY_ADDRESS"] || 'localhost'
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Check for foreign key violation error
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('foreign key') || errorMessage.includes('violates')) {
        this.logger.error(`Foreign key constraint violation: ${errorMessage}`);
        throw new HttpException('User does not exist or database constraint violation', HttpStatus.BAD_REQUEST);
      }

      this.logger.error(`Failed to create connect task: ${error}`);
      throw new HttpException('Failed to create connect task', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get connect tasks for a user
   */
  async getConnectTasks(
    request: DeviceListRequest,
    userId: string
  ): Promise<DeviceListResponse> {
    try {
      const result = await this.nodeRepository.getConnectTasks(
        userId,
        request.page,
        request.pageSize,
        request.status,
        request.search
      );

      return {
        data: [...result.data],
        total: result.total,
        page: result.page,
        pageSize: result.pageSize
      };
    } catch (error) {
      this.logger.error(`Failed to get connect tasks: ${error}`);
      throw new HttpException('Failed to get connect tasks', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Connect a device using a one-time code
   */
  async connectDevice(
    request: DeviceConnectionRequest
  ): Promise<DeviceConnectionResponse> {
    try {
      // Get the connect task by one-time code
      const connectTask = await this.nodeRepository.getConnectTaskByCode(request.one_time_code);

      if (!connectTask) {
        throw new HttpException('Invalid one-time code', HttpStatus.BAD_REQUEST);
      }

      if (connectTask.status !== 'waiting') {
        throw new HttpException('Connect task is not in waiting status', HttpStatus.BAD_REQUEST);
      }

      // 检查设备是否已经注册（device_id不为null）
      if (!connectTask.device_id) {
        // 设备还未注册，只更新连接任务状态为in-progress
        await this.nodeRepository.updateConnectTaskStatus(connectTask.id, 'in-progress');
        this.logger.log(`Connect task ${connectTask.id} status changed from waiting to in-progress (device not yet registered)`);

        return {
          device_id: null, // 设备还未注册
          status: 'in-progress'
        };
      }

      // 设备已注册，更新设备信息
      await this.nodeRepository.updateDeviceInfo(
        connectTask.device_id,
        request.device_info.cpu_model || null,
        request.device_info.cpu_cores || null,
        request.device_info.cpu_threads || null,
        request.device_info.ram_total || null,
        request.device_info.gpu_model || null,
        request.device_info.gpu_count || null,
        request.device_info.gpu_memory || null,
        request.device_info.disk_total || null,
        request.device_info.os_info || null
      );

      // 更新设备状态为connected
      const statusResult = await this.nodeRepository.updateDeviceStatus(connectTask.device_id, 'connected');

      if (statusResult) {
        this.logger.log(`Device ${connectTask.device_id} status changed from ${statusResult.fromStatus} to ${statusResult.toStatus}`);
      }

      return {
        device_id: connectTask.device_id,
        status: 'connected'
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to connect device: ${error}`);
      throw new HttpException('Failed to connect device', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get connect task status
   */
  async getConnectTaskStatus(taskId: string, userId: string): Promise<{task_id: string, status: string, device_id: string, created_at: string, updated_at: string} | null> {
    try {
      return await this.nodeRepository.getConnectTaskById(taskId, userId);
    } catch (error) {
      this.logger.error(`Failed to get connect task status: ${error}`);
      throw new HttpException('Failed to get connect task status', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 检查连接任务超时，每分钟执行一次
   * 如果连接任务30分钟内未完成，则标记为失败
   */
  @Interval(60000) // 每分钟执行一次
  async checkConnectTasksTimeout() {
    try {
      await this.nodeRepository.checkForTimedOutConnectTasks();
    } catch (error) {
      this.logger.error(`Failed to check connect tasks timeout: ${error}`);
    }
  }
}
