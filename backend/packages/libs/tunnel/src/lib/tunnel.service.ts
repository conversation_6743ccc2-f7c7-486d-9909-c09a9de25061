import {Inject, Injectable, Logger} from '@nestjs/common';
import {
  TunnelMessageListener,
  TunnelService as TunnelServiceInterface,
  StreamHandlerOptions,
  NoStreamHandlerOptions,
  SendToDeviceOptions
} from './tunnel.interface';
import {
  TunnelMessage
} from '@saito/models';
import {MessageHandlerRegistry} from "./message-handler/message-handler.registry";
import {UnknownMessageTypeError} from "./errors/unknown-message-type.error";
import { MessageGateway } from './message-gateway/message-gateway.interface';

@Injectable()
export class TunnelServiceImpl implements TunnelServiceInterface {
  private readonly logger = new Logger(TunnelServiceImpl.name);

  private listeners: TunnelMessageListener[] = [];

  constructor(
    @Inject() private readonly handlerRegistry: MessageHandlerRegistry,
    @Inject('MessageGateway') private readonly messageSender: MessageGateway,
    @Inject('PEER_ID') private readonly peerId: string,
    ) {}

  async handleMessage(message: TunnelMessage, listener?: TunnelMessageListener): Promise<void> {
    // this.logger.log("TunnelServiceImpl.handleMessage", message);
    if(message.from === message.to) {
      return;
    }

    // Use injected peerId
    if(message.to === this.peerId) {
      await this.handleIncomeMessage(message, listener);
    } else if (message.from === this.peerId) {
      await this.handleOutcomeMessage(message, listener);
    } else {
      this.logger.warn(`Ignore message not related to ${this.peerId}: ${message.from} -> ${message.to}`);
    }
  }

  // Send this message to actual target
  async sendMessage(message: TunnelMessage): Promise<void> {
    return this.messageSender.sendMessage(message);
  }

  private async handleIncomeMessage(message: TunnelMessage, listener?: TunnelMessageListener): Promise<void> {
    this.logger.debug(`[TunnelService] Handling income message:`, {
      type: message.type,
      from: message.from,
      to: message.to,
      hasPayload: !!message.payload
    });

    // 先触发监听器，并检查是否有监听器处理了消息
    const listenerHandled = await this.triggerListener(message);

    // 如果有监听器处理了消息，并且是 chat_response_stream 类型，则跳过旧架构处理
    if (listenerHandled && message.type === 'chat_response_stream') {
      this.logger.log(`[TunnelService] Message ${message.type} handled by listener, skipping legacy handler`);
    } else {
      // 使用旧架构处理器
      const handler = this.handlerRegistry.getIncomeHandler(message.type);
      if(!handler) {
        this.logger.error(`[TunnelService] Cannot handle income message ${message.type}`);
        this.logger.debug(`[TunnelService] Available income handlers:`, this.handlerRegistry.getAllIncomeHandlers());
        throw new UnknownMessageTypeError(message.type, 'income');
      }
      this.logger.debug(`[TunnelService] Using handler: ${handler.constructor.name} for message type: ${message.type}`);
      await handler.handleMessage(message);
    }

    // attach listener at the end to avoid self-trigger
    if (listener) {
      this.listeners.push(listener);
    }
  }

  private async handleOutcomeMessage(message: TunnelMessage, listener?: TunnelMessageListener): Promise<void> {
    // await this.triggerListener(message);

    const handler = this.handlerRegistry.getOutcomeHandler(message.type);
    if(!handler) {
      this.logger.error(`Cannot handle income message ${message.type}`);
      throw new UnknownMessageTypeError(message.type, 'outcome');
    }
    await handler.handleMessage(message);

    // attach listener at the end to avoid self-trigger
    if (listener) {
      this.listeners.push(listener);
      // console.log('listener added', listener);
      // console.log('current time', Date.now());
      // console.log('current listeners', this.listeners);
    }
  }

  /**
   * Checks if existing listener matches the message and calls the callback if it does.
   * @param message
   * @private
   * @returns boolean - 是否有监听器处理了消息
   */
  private async triggerListener(message: TunnelMessage): Promise<boolean> {
    const remaining: TunnelMessageListener[] = [];
    let hasMatchingListener = false;

    // 添加调试日志
    if (message.type === 'chat_response_stream') {
      this.logger.debug(`[TunnelService] Triggering listeners for chat_response_stream. Total listeners: ${this.listeners.length}`);
    }

    for (const _listener of this.listeners) {
      const isMatch = _listener.match(message);

      if (isMatch) {
        hasMatchingListener = true;
        if (message.type === 'chat_response_stream') {
          this.logger.debug(`[TunnelService] Found matching listener for chat_response_stream, triggering callback`);
        }
        _listener.callback(message);

        const shouldRemove = _listener.once?.(message) ?? false;
        if (!shouldRemove) {
          remaining.push(_listener);
        }
      } else {
        remaining.push(_listener);
      }
    }

    this.listeners = remaining;
    return hasMatchingListener;
  }


  /**
   * 发送消息到设备
   * @param options 发送选项
   */
  async handleSendToDevice(options: SendToDeviceOptions): Promise<void> {
    this.logger.log(`Attempting to send message to device ${options.deviceId}`);

    // 首先检查设备是否连接
    const isConnected = await this.isDeviceConnected(options.deviceId);
    if (!isConnected) {
      const errorMessage = `Device ${options.deviceId} is not connected`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    const message: TunnelMessage = {
      from: this.peerId,
      to: options.deviceId,
      type: 'test', // 使用通用的测试消息类型
      payload: options.message
    };

    try {
      await this.sendMessage(message);
      this.logger.log(`Message sent successfully to device ${options.deviceId}`);
    } catch (error) {
      this.logger.error(`Failed to send message to device ${options.deviceId}: ${error}`);
      throw error;
    }
  }

  /**
   * 检查设备是否连接
   * @param deviceId 设备ID
   * @returns 是否连接
   */
  async isDeviceConnected(deviceId: string): Promise<boolean> {
    try {
      this.logger.debug(`Checking connection status for device ${deviceId}`);

      // 使用 MessageGateway 的新方法检查设备连接状态
      const gateway = this.messageSender as unknown as { isDeviceConnected?: (deviceId: string) => boolean };
      if (gateway && typeof gateway.isDeviceConnected === 'function') {
        const isConnected = gateway.isDeviceConnected(deviceId);
        this.logger.debug(`Device ${deviceId} connection status: ${isConnected}`);
        return isConnected;
      }

      // 如果无法访问 MessageGateway 的方法，返回 false
      this.logger.warn(`Cannot check device connection status for ${deviceId}, MessageGateway method not available`);
      return false;
    } catch (error) {
      this.logger.error(`Error checking device connection for ${deviceId}: ${error}`);
      return false;
    }
  }

  /**
   * 验证设备连接（发送 ping 并等待响应）
   * @param deviceId 设备ID
   * @param timeoutMs 超时时间（毫秒）
   * @returns 是否连接
   */
  async verifyDeviceConnection(deviceId: string, timeoutMs: number = 5000): Promise<boolean> {
    try {
      this.logger.debug(`Verifying device connection for ${deviceId} with ${timeoutMs}ms timeout`);

      // 首先检查基本的 WebSocket 连接
      const basicCheck = await this.isDeviceConnected(deviceId);
      if (!basicCheck) {
        this.logger.debug(`Device ${deviceId} failed basic connection check`);
        return false;
      }

      // 如果基本检查通过，发送 ping 消息验证连接
      const pingId = `ping-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          this.logger.debug(`Ping timeout for device ${deviceId}`);
          resolve(false);
        }, timeoutMs);

        // 注册 pong 响应监听器
        const listener: TunnelMessageListener = {
          match: (msg: TunnelMessage) => {
            return msg.from === deviceId &&
                   msg.type === 'pong' &&
                   msg.payload &&
                   typeof msg.payload === 'object' &&
                   'message' in msg.payload &&
                   msg.payload.message === pingId;
          },
          callback: () => {
            this.logger.debug(`Received pong from device ${deviceId}`);
            clearTimeout(timeout);
            resolve(true);
          },
          once: () => true // 只处理一次
        };

        this.listeners.push(listener);

        // 发送 ping 消息
        const pingMessage: TunnelMessage = {
          from: this.peerId,
          to: deviceId,
          type: 'ping',
          payload: { message: pingId, timestamp: Date.now() },
          timestamp: Date.now()
        } as TunnelMessage;

        this.sendMessage(pingMessage).catch((error) => {
          this.logger.error(`Failed to send ping to device ${deviceId}: ${error}`);
          clearTimeout(timeout);
          resolve(false);
        });
      });
    } catch (error) {
      this.logger.error(`Error verifying device connection for ${deviceId}: ${error}`);
      return false;
    }
  }

  /**
   * 添加监听器
   * @param listener 监听器
   */
  addListener(listener: TunnelMessageListener): void {
    this.listeners.push(listener);
    this.logger.debug(`[TunnelService] Added listener. Total listeners: ${this.listeners.length}`);
  }

  /**
   * 移除监听器
   * @param listener 监听器
   */
  removeListener(listener: TunnelMessageListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
      this.logger.debug(`[TunnelService] Removed listener. Total listeners: ${this.listeners.length}`);
    }
  }
}

export const TunnelServiceProvider = {
  provide: 'TunnelService',
  useClass: TunnelServiceImpl,
};
