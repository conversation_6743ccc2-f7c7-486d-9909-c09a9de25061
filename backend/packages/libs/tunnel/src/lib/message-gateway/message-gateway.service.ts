import {forwardRef, Inject, Injectable, OnModuleInit} from "@nestjs/common";
import {TunnelMessage} from "@saito/models";
import {MessageGateway} from "./message-gateway.interface";
import {MessageBody, SubscribeMessage, WebSocketGateway, WebSocketServer, ConnectedSocket} from "@nestjs/websockets";
import {Server, Socket} from "socket.io";
import {UnknownPeerError} from "../errors/unknown-peer.error";
import { TunnelService } from "../tunnel.interface";

@Injectable()
@WebSocketGateway()
export class MessageGatewayServiceImpl implements MessageGateway, OnModuleInit {

  constructor(
    @Inject(forwardRef(() => 'TunnelService'))
    private readonly tunnelService: TunnelService
  ) {}
  @WebSocketServer()
  server!: Server; // WebSocket server instance
  private peers: Map<string, Socket> = new Map(); // Socket ID -> Socket
  private deviceToPeer: Map<string, string> = new Map(); // Device ID -> Socket ID
  private peerToDevice: Map<string, string> = new Map(); // Socket ID -> Device ID

  onModuleInit() {
    // Periodic heartbeat check to clean up inactive connections
    setInterval(() => {
      this.checkInactivePeers();
    }, 60000); // Check every minute
  }

  checkInactivePeers() {
    this.peers.forEach((socket, peerId) => {
      if (!socket.connected) {
        console.log(`Peer ${peerId} is inactive, cleaning up.`);
        this.peers.delete(peerId);

        // 清理设备映射
        const deviceId = this.peerToDevice.get(peerId);
        if (deviceId) {
          this.deviceToPeer.delete(deviceId);
          this.peerToDevice.delete(peerId);
          console.log(`Cleaned up device mapping for device ${deviceId}`);
        }
      }
    });
  }

  // When a new client connects
  handleConnection(client: Socket) {
    try {
      console.log('Peer connected with ID:', client.id);
      this.peers.set(client.id, client);
    } catch (error) {
      console.error('Error handling connection:', error);
    }
  }

  // When a client disconnects
  handleDisconnect(client: Socket) {
    try {
      console.log('Peer disconnected with ID:', client.id);
      this.peers.delete(client.id);

      // 清理设备映射
      const deviceId = this.peerToDevice.get(client.id);
      if (deviceId) {
        this.deviceToPeer.delete(deviceId);
        this.peerToDevice.delete(client.id);
        console.log(`Device ${deviceId} disconnected (peer ${client.id})`);
      }
    } catch (error) {
      console.error('Error handling disconnection:', error);
    }
  }

  // Handling messages from the client
  @SubscribeMessage('message')
  async handleMessage(@ConnectedSocket() client: Socket, @MessageBody() message: TunnelMessage): Promise<void> {
    try {
      console.log(`[MessageGateway] Received message from client ${client.id}:`, {
        type: message.type,
        from: message.from,
        to: message.to,
        hasPayload: !!message.payload
      });

      // 检查是否是设备注册消息
      if (message.type === 'device_register' && message.payload && typeof message.payload === 'object' && 'deviceId' in message.payload) {
        const deviceId = message.payload.deviceId as string;
        console.log(`[MessageGateway] Registering device ${deviceId} with peer ${client.id}`);

        // 建立设备ID和Socket ID的映射
        this.deviceToPeer.set(deviceId, client.id);
        this.peerToDevice.set(client.id, deviceId);

        // 发送注册确认
        client.emit('message', {
          from: 'gateway',
          to: deviceId,
          type: 'device_register_ack',
          payload: { success: true, deviceId },
          timestamp: Date.now()
        });

        console.log(`[MessageGateway] Device ${deviceId} successfully registered with peer ${client.id}`);
        return;
      }

      console.log(`[MessageGateway] Forwarding message to TunnelService: ${message.type}`);
      await this.tunnelService.handleMessage(message);
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      // 确保抛出的是标准 Error 对象
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`WebSocket message handling failed: ${String(error)}`);
      }
    }
  }

  // Method to send a message to a specific peer using their socketId
  sendMessage(message: TunnelMessage): void {
    try {
      // 首先尝试直接使用 message.to 作为 Socket ID
      let socket = this.peers.get(message.to);

      // 如果没找到，尝试使用设备ID映射
      if (!socket) {
        const peerId = this.deviceToPeer.get(message.to);
        if (peerId) {
          socket = this.peers.get(peerId);
          console.log(`Using device mapping: device ${message.to} -> peer ${peerId}`);
        }
      }

      if (!socket) {
        console.log(`Available peers: ${Array.from(this.peers.keys()).join(', ')}`);
        console.log(`Available device mappings: ${Array.from(this.deviceToPeer.entries()).map(([d, p]) => `${d}->${p}`).join(', ')}`);
        throw new UnknownPeerError(message.to);
      }

      socket.emit("message", message);
    } catch (error) {
      console.error('Error sending message:', error);
      // 确保抛出的是标准 Error 对象
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to send message: ${String(error)}`);
      }
    }
  }

  // 添加一个方法来检查设备是否连接（供 TunnelService 使用）
  isDeviceConnected(deviceId: string): boolean {
    // 首先检查是否直接作为 peer ID 存在
    if (this.peers.has(deviceId)) {
      const socket = this.peers.get(deviceId);
      return socket?.connected || false;
    }

    // 然后检查设备ID映射
    const peerId = this.deviceToPeer.get(deviceId);
    if (peerId && this.peers.has(peerId)) {
      const socket = this.peers.get(peerId);
      return socket?.connected || false;
    }

    return false;
  }

  // 调试方法：获取当前连接状态
  getConnectionStatus() {
    return {
      totalPeers: this.peers.size,
      totalDeviceMappings: this.deviceToPeer.size,
      peers: Array.from(this.peers.keys()),
      deviceMappings: Array.from(this.deviceToPeer.entries()).map(([deviceId, peerId]) => ({
        deviceId,
        peerId,
        connected: this.peers.get(peerId)?.connected || false
      }))
    };
  }

  // 手动注册设备（用于测试）
  manualRegisterDevice(deviceId: string, peerId: string) {
    if (this.peers.has(peerId)) {
      this.deviceToPeer.set(deviceId, peerId);
      this.peerToDevice.set(peerId, deviceId);
      console.log(`Manually registered device ${deviceId} with peer ${peerId}`);
      return true;
    }
    console.log(`Cannot register device ${deviceId}: peer ${peerId} not found`);
    return false;
  }
}

export const MessageGatewayProvider = {
  provide: 'MessageGateway',
  useClass: MessageGatewayServiceImpl,
};
