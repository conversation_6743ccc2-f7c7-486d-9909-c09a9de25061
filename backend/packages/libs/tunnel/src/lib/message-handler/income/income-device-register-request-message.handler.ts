import { Inject, Injectable, Logger } from "@nestjs/common";
import { IncomeBaseMessageHandler } from "../base-message-handler";
import {
  DeviceRegisterRequestMessageSchema,
  DeviceRegisterResponseMessageSchema,
  TunnelMessage
} from "@saito/models";
import { TunnelService } from "../../tunnel.interface";
import { MessageHandler } from "../message-handler.decorator";
import { NodeService } from "@saito/node";

@MessageHandler({ type: 'device_register_request', direction: 'income' })
@Injectable()
export class IncomeDeviceRegisterRequestMessageHandler extends IncomeBaseMessageHandler {
  private readonly logger = new Logger(IncomeDeviceRegisterRequestMessageHandler.name);

  constructor(
    @Inject('TunnelService') private readonly tunnel: TunnelService,
    @Inject('PEER_ID') protected override readonly peerId: string,
    private readonly nodeService: NodeService
  ) {
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    try {
      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Received device registration message:`, {
        type: message.type,
        from: message.from,
        to: message.to,
        payload: message.payload
      });

      const registerRequestMessage = DeviceRegisterRequestMessageSchema.parse(message);
      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Processing device registration from ${message.from}`);
      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Registration payload:`, registerRequestMessage.payload);

      // 确保设备ID使用消息发送者的ID（DID格式）
      const registrationPayload = {
        ...registerRequestMessage.payload,
        device_id: message.from // 使用消息发送者的DID作为设备ID
      };

      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Enhanced registration payload:`, registrationPayload);

      // 调用设备注册服务创建设备记录
      const registrationResult = await this.nodeService.registerDevice(registrationPayload);
      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Device registration successful:`, registrationResult);

      // 构建成功响应消息
      const responseMessage = DeviceRegisterResponseMessageSchema.parse({
        type: 'device_register_response',
        from: this.peerId,
        to: registerRequestMessage.from,
        payload: {
          device_id: registrationResult.device_id,
          status: registrationResult.status,
          device_type: registrationResult.device_type,
          reward_address: registrationResult.reward_address
        }
      });

      // 发送响应
      await this.tunnel.handleMessage(responseMessage);

      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Device registration completed successfully for ${registrationResult.device_id}`);
    } catch (error) {
      this.logger.error(`[IncomeDeviceRegisterRequestHandler] Failed to process device registration: ${error}`);
      this.logger.error(`[IncomeDeviceRegisterRequestHandler] Error stack:`, error instanceof Error ? error.stack : 'No stack trace');

      // 发送错误响应
      try {
        const errorResponse = DeviceRegisterResponseMessageSchema.parse({
          type: 'device_register_response',
          from: this.peerId,
          to: message.from,
          payload: {
            device_id: message.from, // 使用发送者ID作为设备ID
            status: 'failed',
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        });

        await this.tunnel.handleMessage(errorResponse);
      } catch (responseError) {
        this.logger.error(`[IncomeDeviceRegisterRequestHandler] Failed to send error response: ${responseError}`);
      }
    }
  }
}
