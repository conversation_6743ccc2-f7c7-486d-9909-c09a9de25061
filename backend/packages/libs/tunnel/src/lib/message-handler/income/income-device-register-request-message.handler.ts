import { Inject, Injectable, Logger } from "@nestjs/common";
import { IncomeBaseMessageHandler } from "../base-message-handler";
import {
  DeviceRegisterRequestMessageSchema,
  DeviceRegisterResponseMessageSchema,
  TunnelMessage
} from "@saito/models";
import { TunnelService } from "../../tunnel.interface";
import { MessageHandler } from "../message-handler.decorator";
import { NodeService } from "@saito/node";

@MessageHandler({ type: 'device_register_request', direction: 'income' })
@Injectable()
export class IncomeDeviceRegisterRequestMessageHandler extends IncomeBaseMessageHandler {
  private readonly logger = new Logger(IncomeDeviceRegisterRequestMessageHandler.name);

  constructor(
    @Inject('TunnelService') private readonly tunnel: TunnelService,
    @Inject('PEER_ID') protected override readonly peerId: string,
    private readonly nodeService: NodeService
  ) {
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    try {
      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Received message:`, {
        type: message.type,
        from: message.from,
        to: message.to,
        payload: message.payload
      });

      const registerRequestMessage = DeviceRegisterRequestMessageSchema.parse(message);
      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Processing device registration from ${message.from}`);
      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Registration payload:`, registerRequestMessage.payload);

      // 调用现有的设备注册服务
      const registrationResult = await this.nodeService.registerDevice(registerRequestMessage.payload);
      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Registration result:`, registrationResult);

      // 构建响应消息
      const responseMessage = DeviceRegisterResponseMessageSchema.parse({
        type: 'device_register_response',
        from: this.peerId,
        to: registerRequestMessage.from,
        payload: registrationResult
      });

      // 发送响应
      await this.tunnel.handleMessage(responseMessage);

      this.logger.log(`[IncomeDeviceRegisterRequestHandler] Device registration completed for ${registrationResult.device_id}`);
    } catch (error) {
      this.logger.error(`[IncomeDeviceRegisterRequestHandler] Failed to process device registration: ${error}`);
      this.logger.error(`[IncomeDeviceRegisterRequestHandler] Error details:`, error);

      // 发送错误响应
      const errorResponse = DeviceRegisterResponseMessageSchema.parse({
        type: 'device_register_response',
        from: this.peerId,
        to: message.from,
        payload: {
          device_id: '',
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });

      await this.tunnel.handleMessage(errorResponse);
    }
  }
}
