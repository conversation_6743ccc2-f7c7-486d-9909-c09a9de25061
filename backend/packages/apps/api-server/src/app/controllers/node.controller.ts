import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Param,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Logger
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { NodeService } from "@saito/node";
import { JwtAuthGuard } from "@saito/auth";
import { EarningsService } from "@saito/earnings";
import {
  CreateConnectTaskRequest,
  DeviceConnectionRequest,
  DeviceListRequest,
  DeviceRegisterRequest,
  NewDeviceHeartbeatRequest,
  GetDeviceEarningsRequest,
  DeviceModelReportRequest
} from '@saito/models';
// AuthenticatedRequest is still needed for createConnectTask endpoint
import { AuthenticatedRequest } from "../types/request.types";

@ApiTags('node')
@Controller('node')
export class NodeController {
  private readonly logger = new Logger(NodeController.name);

  constructor(
    private readonly nodeService: NodeService,
    private readonly earningsService: EarningsService
  ) {}

  @Post('connect-task')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create a connect task',
    description: 'Creates a new connect task for a device'
  })
  @ApiBody({
    description: 'The connect task information',
    type: Object,
    schema: {
      properties: {
        task_name: {
          type: 'string',
          description: 'The name of the task',
        },
        signature: {
          type: 'string',
          description: 'The signature of the message',
        },
        device_type: {
          type: 'string',
          description: 'The type of the device (optional)',
        },
        gpu_type: {
          type: 'string',
          description: 'The type of the GPU (optional)',
        },
      },
      required: ['task_name', 'signature'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Connect task created successfully',
    schema: {
      properties: {
        task_id: {
          type: 'string',
          description: 'The ID of the created task',
        },
        one_time_code: {
          type: 'string',
          description: 'The one-time code for device connection',
        },
        gateway_address: {
          type: 'string',
          description: 'The address of the gateway',
        },
      },
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createConnectTask(
    @Body() createConnectTaskDto: CreateConnectTaskRequest,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const userId = req.user.userId;
      // 确保 walletAddress 不为 undefined
      const walletAddress = req.user.walletAddress || '';


      const result = await this.nodeService.createConnectTask(
        createConnectTaskDto,
        userId,
        walletAddress
      );

      // 返回统一格式的响应
      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to create connect task: ${error}`);
      throw new HttpException('Failed to create connect task', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('connect-task/:id/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Check connect task status',
    description: 'Checks the status of a specific connect task regardless of user'
  })
  @ApiParam({ name: 'id', description: 'Connect task ID' })
  @ApiResponse({
    status: 200,
    description: 'Connect task status retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            task_id: { type: 'string' },
            status: { type: 'string' },
            device_id: { type: 'string' },
            created_at: { type: 'string' },
            updated_at: { type: 'string' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Connect task not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async checkConnectTaskStatus(
    @Param('id') id: string
  ) {
    try {
      // Pass empty string instead of userId to get all records without filtering by user
      const taskStatus = await this.nodeService.getConnectTaskStatus(id, '');

      if (!taskStatus) {
        throw new HttpException('Connect task not found', HttpStatus.NOT_FOUND);
      }

      return {
        success: true,
        data: taskStatus
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to check connect task status: ${error}`);
      throw new HttpException('Failed to check connect task status', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('connect-task-list')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get connect tasks',
    description: 'Gets a list of all connect tasks regardless of user'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Page size (default: 10)' })
  @ApiQuery({ name: 'status', required: false, type: String, description: 'Filter by status' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiResponse({
    status: 200,
    description: 'Connect tasks retrieved successfully',
    schema: {
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              device_id: { type: 'string' },
              task_name: { type: 'string' },
              device_type: { type: 'string' },
              gpu_type: { type: 'string' },
              status: { type: 'string' },
              created_at: { type: 'string' },
              updated_at: { type: 'string' },
              current_ai_model: { type: 'string' },
              connected_gateway: { type: 'string' }
            }
          }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        pageSize: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getConnectTasks(
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('status') status: string,
    @Query('search') search: string
  ) {
    try {
      const request: DeviceListRequest = {
        page,
        pageSize,
        status,
        search
      };

      // Pass empty string instead of userId to get all records without filtering by user
      const result = await this.nodeService.getConnectTasks(request, '');

      // 返回统一格式的响应
      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to get connect tasks: ${error}`);
      throw new HttpException('Failed to get connect tasks', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('connect')
  @ApiOperation({
    summary: 'Connect a device',
    description: 'Connects a device using a one-time code'
  })
  @ApiBody({
    description: 'The device connection information',
    type: Object,
    schema: {
      properties: {
        one_time_code: {
          type: 'string',
          description: 'The one-time code for device connection',
        },
        device_info: {
          type: 'object',
          properties: {
            cpu_model: { type: 'string' },
            cpu_cores: { type: 'number' },
            cpu_threads: { type: 'number' },
            ram_total: { type: 'number' },
            gpu_model: { type: 'string' },
            gpu_count: { type: 'number' },
            gpu_memory: { type: 'number' },
            disk_total: { type: 'number' },
            os_info: { type: 'string' }
          }
        }
      },
      required: ['one_time_code'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Device connected successfully',
    schema: {
      properties: {
        device_id: {
          type: 'string',
          description: 'The ID of the connected device',
        },
        status: {
          type: 'string',
          description: 'The status of the device',
        },
      },
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async connectDevice(
    @Body() connectDeviceDto: DeviceConnectionRequest
  ) {
    try {
      const result = await this.nodeService.connectDevice(connectDeviceDto);

      // 返回统一格式的响应
      return {
        success: true,
        data: result
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to connect device: ${error}`);
      throw new HttpException('Failed to connect device', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // @Post('heartbeat')
  // @ApiOperation({
  //   summary: 'Update device heartbeat',
  //   description: 'Updates the heartbeat of a device'
  // })
  // @ApiBody({
  //   description: 'The device heartbeat information',
  //   type: Object,
  //   schema: {
  //     properties: {
  //       device_id: {
  //         type: 'string',
  //         description: 'The ID of the device',
  //       },
  //       status: {
  //         type: 'string',
  //         description: 'The status of the device',
  //       },
  //       cpu_usage_percent: {
  //         type: 'number',
  //         description: 'CPU usage percentage',
  //       },
  //       ram_usage_percent: {
  //         type: 'number',
  //         description: 'RAM usage percentage',
  //       },
  //       gpu_usage_percent: {
  //         type: 'number',
  //         description: 'GPU usage percentage',
  //       },
  //       gpu_temperature: {
  //         type: 'number',
  //         description: 'GPU temperature',
  //       },
  //       network_in_kbps: {
  //         type: 'number',
  //         description: 'Network inbound traffic in kbps',
  //       },
  //       network_out_kbps: {
  //         type: 'number',
  //         description: 'Network outbound traffic in kbps',
  //       },
  //       uptime_seconds: {
  //         type: 'number',
  //         description: 'Device uptime in seconds',
  //       },
  //       model: {
  //         type: 'string',
  //         description: 'Current model running on the device',
  //       }
  //     },
  //     required: ['device_id', 'status'],
  //   },
  // })
  // @ApiResponse({ status: 200, description: 'Heartbeat updated successfully' })
  // @ApiResponse({ status: 500, description: 'Internal server error' })
  // async updateDeviceHeartbeat(
  //   @Body() heartbeatDto: DeviceHeartbeatRequest
  // ) {
  //   try {
  //     await this.nodeService.updateDeviceHeartbeat(heartbeatDto);

  //     // 返回统一格式的响应
  //     return {
  //       success: true,
  //       data: null
  //     };
  //   } catch (error) {
  //     this.logger.error(`Failed to update device heartbeat: ${error}`);
  //     throw new HttpException('Failed to update device heartbeat', HttpStatus.INTERNAL_SERVER_ERROR);
  //   }
  // }

  @Get('devices')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get devices',
    description: 'Gets a list of all devices regardless of user'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Page size (default: 10)' })
  @ApiQuery({ name: 'status', required: false, type: String, description: 'Filter by status' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'onlyMyDevices', required: false, type: String, description: 'Show only devices owned by the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'Devices retrieved successfully',
    schema: {
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              device_id: { type: 'string' },
              task_name: { type: 'string' },
              device_type: { type: 'string' },
              gpu_type: { type: 'string' },
              status: { type: 'string' },
              current_ai_model: { type: 'string' },
              connected_gateway: { type: 'string' },
              total_earnings: { type: 'number' },
              pending_earnings: { type: 'number' }
            }
          }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        pageSize: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getDevices(
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('status') status: string,
    @Query('search') search: string,
    @Query('onlyMyDevices') onlyMyDevices: string,
    @Request() req: AuthenticatedRequest
  ) {
    try {
      const request: DeviceListRequest = {
        page,
        pageSize,
        status,
        search,
        onlyMyDevices
      };
      this.logger.debug(`getDevices request: ${onlyMyDevices} ${JSON.stringify(req.user)}`);
      const userId = onlyMyDevices === 'true' ? req.user.userId : '';
      const result = await this.nodeService.getDevices(request, userId);

      // 返回统一格式的响应
      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to get devices: ${error}`);
      throw new HttpException('Failed to get devices', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('devices/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get device by ID',
    description: 'Gets a device by ID regardless of user'
  })
  @ApiParam({ name: 'id', description: 'Device ID' })
  @ApiResponse({
    status: 200,
    description: 'Device retrieved successfully',
    schema: {
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        user_id: { type: 'string' },
        owner_address: { type: 'string' },
        reward_address: { type: 'string' },
        status: { type: 'string' },
        device_type: { type: 'string' },
        cpu_model: { type: 'string' },
        cpu_cores: { type: 'number' },
        cpu_threads: { type: 'number' },
        cpu_usage_percent: { type: 'number' },
        ram_total: { type: 'number' },
        ram_available: { type: 'number' },
        gpu_model: { type: 'string' },
        gpu_count: { type: 'number' },
        gpu_memory: { type: 'number' },
        gpu_temperature: { type: 'number' },
        disk_total: { type: 'number' },
        disk_available: { type: 'number' },
        ip_address: { type: 'string' },
        last_ping: { type: 'string' },
        latency: { type: 'number' },
        uptime_seconds: { type: 'number' },
        last_boot: { type: 'string' },
        os_info: { type: 'string' },
        created_at: { type: 'string' },
        updated_at: { type: 'string' },
        last_error: { type: 'string' },
        firmware_version: { type: 'string' },
        software_version: { type: 'string' },
        last_maintenance_at: { type: 'string' },
        next_maintenance_at: { type: 'string' },
        health_score: { type: 'number' },
        tags: { type: 'array', items: { type: 'string' } },
        current_model: { type: 'string' },
        total_earnings: { type: 'number' },
        pending_earnings: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getDevice(
    @Param('id') id: string
  ) {
    try {
      // Pass empty string instead of userId to get all records without filtering by user
      const device = await this.nodeService.getDevice(id, '');

      if (!device) {
        throw new HttpException('Device not found', HttpStatus.NOT_FOUND);
      }

      // 返回统一格式的响应
      return {
        success: true,
        data: device
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to get device: ${error}`);
      throw new HttpException('Failed to get device', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('devices/:id/status-history')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get device status history',
    description: 'Gets the status history of a device regardless of user'
  })
  @ApiParam({ name: 'id', description: 'Device ID' })
  @ApiQuery({ name: 'days', required: false, type: Number, description: 'Number of days to look back (default: 7)' })
  @ApiResponse({
    status: 200,
    description: 'Device status history retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          device_id: { type: 'string' },
          from_status: { type: 'string' },
          to_status: { type: 'string' },
          change_time: { type: 'string' },
          date: { type: 'string' },
          created_at: { type: 'string' }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getDeviceStatusHistory(
    @Param('id') id: string,
    @Query('days') days: number = 7
  ) {
    try {
      // Pass empty string instead of userId to get all records without filtering by user
      const history = await this.nodeService.getDeviceStatusHistory(id, '', days);

      // 返回统一格式的响应
      return {
        success: true,
        data: history
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to get device status history: ${error}`);
      throw new HttpException('Failed to get device status history', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('devices/:id/tasks')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get device tasks',
    description: 'Gets the tasks of a device regardless of user'
  })
  @ApiParam({ name: 'id', description: 'Device ID' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Page size (default: 10)' })
  @ApiQuery({ name: 'status', required: false, type: String, description: 'Filter by status' })
  @ApiResponse({
    status: 200,
    description: 'Device tasks retrieved successfully',
    schema: {
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              device_id: { type: 'string' },
              model: { type: 'string' },
              created_at: { type: 'string' },
              status: { type: 'string' },
              total_duration: { type: 'number' },
              load_duration: { type: 'number' },
              prompt_eval_count: { type: 'number' },
              prompt_eval_duration: { type: 'number' },
              eval_count: { type: 'number' },
              eval_duration: { type: 'number' },
              updated_at: { type: 'string' }
            }
          }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        pageSize: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getDeviceTasks(
    @Param('id') id: string,
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('status') status?: string
  ) {
    try {
      const request: DeviceListRequest = {
        page,
        pageSize,
        status
      };

      // Pass empty string instead of userId to get all records without filtering by user
      const tasks = await this.nodeService.getDeviceTasks(id, '', request);

      // 返回统一格式的响应
      return {
        success: true,
        data: tasks
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to get device tasks: ${error}`);
      throw new HttpException('Failed to get device tasks', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('devices/:id/earnings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get device earnings',
    description: 'Gets all earnings of a device regardless of user'
  })
  @ApiParam({ name: 'id', description: 'Device ID' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Page size (default: 10)' })
  @ApiQuery({ name: 'status', required: false, type: String, description: 'Filter by status' })
  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for data' })
  @ApiResponse({
    status: 200,
    description: 'Device earnings retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  block_rewards: { type: 'number' },
                  job_rewards: { type: 'number' },
                  total_rewards: { type: 'number' },
                  date: { type: 'string' },
                  created_at: { type: 'string' },
                  device_name: { type: 'string' },
                  model: { type: 'string' },
                  task_id: {  type: 'string' }
                }
              }
            },
            total: { type: 'number' },
            page: { type: 'number' },
            pageSize: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getDeviceEarnings(
    @Param('id') id: string,
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('status') status?: string,
    @Query('timeRange') timeRange?: string
  ) {
    try {
      const request: GetDeviceEarningsRequest = {
        page,
        pageSize,
        status,
        timeRange
      };

      // Pass empty string instead of userId to get all records without filtering by user
      const result = await this.earningsService.getDeviceEarnings(id, request, '');

      return {
        success: true,
        data: result
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to get device earnings: ${error}`);
      throw new HttpException('Failed to get device earnings', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('register-')
  @ApiOperation({
    summary: 'Register device',
    description: 'Registers a device using a one-time code'
  })
  @ApiBody({
    schema: {
      properties: {
        code: { type: 'string' },
        gateway_address: { type: 'string' },
        reward_address: { type: 'string' },
        device_type: { type: 'string' },
        gpu_type: { type: 'string' },
        ip: { type: 'string' },
        local_models: { type: 'object' }
      },
      required: ['code', 'gateway_address', 'reward_address']
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Device registered successfully',
    schema: {
      properties: {
        device_id: { type: 'string' },
        status: { type: 'string' },
        device_type: { type: 'string' },
        reward_address: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async registerDevice(
    @Body() request: DeviceRegisterRequest
  ) {
    try {
      const result = await this.nodeService.registerDevice(request);

      // 返回统一格式的响应
      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`Failed to register device: ${error}`);
      throw new HttpException('Failed to register device', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('heartbeat/new-')
  @ApiOperation({
    summary: 'Update device heartbeat (new format)',
    description: 'Updates the heartbeat of a device using the new format'
  })
  @ApiBody({
    schema: {
      properties: {
        code: { type: 'string' },
        cpu_usage: { type: 'number' },
        memory_usage: { type: 'number' },
        gpu_usage: { type: 'number' },
        ip: { type: 'string' },
        timestamp: { type: 'string' },
        type: { type: 'string' },
        model: { type: 'string' },
        device_info: {
          type: 'object',
          properties: {
            cpu_model: { type: 'string' },
            cpu_cores: { type: 'number' },
            cpu_threads: { type: 'number' },
            ram_total: { type: 'number' },
            gpu_model: { type: 'string' },
            gpu_count: { type: 'number' },
            gpu_memory: { type: 'number' },
            disk_total: { type: 'number' },
            os_info: { type: 'string' },
          }
        },
        gateway_url: { type: 'string' },
        device_id: { type: 'string' }
      },
      required: ['code']
    }
  })
  @ApiResponse({ status: 200, description: 'Heartbeat updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async updateHeartbeatNew(
    @Body() request: NewDeviceHeartbeatRequest
  ) {
    try {
      await this.nodeService.updateDeviceHeartbeatNew(request);

      // 返回统一格式的响应
      return {
        success: true,
        data: null
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to update heartbeat: ${error}`);
      throw new HttpException('Failed to update heartbeat', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('devices/:id/models')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get device models',
    description: 'Gets the models supported by a device regardless of user'
  })
  @ApiParam({ name: 'id', description: 'Device ID' })
  @ApiResponse({
    status: 200,
    description: 'Device models retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              device_id: { type: 'string' },
              model_name: { type: 'string' },
              model_family: { type: 'string' },
              parameter_size: { type: 'string' },
              quantization_level: { type: 'string' },
              format: { type: 'string' },
              size_bytes: { type: 'number' },
              digest: { type: 'string' },
              modified_at: { type: 'string' },
              created_at: { type: 'string' },
              updated_at: { type: 'string' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getDeviceModels(
    @Param('id') id: string
  ) {
    try {
      // Pass empty string instead of userId to get all records without filtering by user
      const models = await this.nodeService.getDeviceModels(id, '');

      return {
        success: true,
        data: models
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Failed to get device models: ${error}`);
      throw new HttpException('Failed to get device models', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('models')
  @ApiOperation({
    summary: 'Get models',
    description: 'Gets all models'
  })
  @ApiResponse({
    status: 200,
    description: 'Models retrieved successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              device_id: { type: 'string' },
              model_name: { type: 'string' },
              model_family: { type: 'string' },
              parameter_size: { type: 'string' },
              quantization_level: { type: 'string' },
              format: { type: 'string' },
              size_bytes: { type: 'number' },
              digest: { type: 'string' },
              modified_at: { type: 'string' },
              created_at: { type: 'string' },
              updated_at: { type: 'string' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getModels() {
    try {
      const models = await this.nodeService.getModels();

      return {
        success: true,
        data: models
      };
    } catch (error) {
      this.logger.error(`Failed to get models: ${error}`);
      throw new HttpException('Failed to get models', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('online-models')
  @ApiOperation({
    summary: 'Get online device models',
    description: 'Gets all models from online devices in the specified format (ollama or openai)'
  })
  @ApiQuery({
    name: 'format',
    required: false,
    type: String,
    description: 'Response format (ollama or openai)',
    enum: ['ollama', 'openai']
  })
  @ApiResponse({
    status: 200,
    description: 'Online device models retrieved successfully',
    schema: {
      oneOf: [
        {
          // Ollama format
          properties: {
            models: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  modified_at: { type: 'string' },
                  size: { type: 'number' },
                  digest: { type: 'string' },
                  details: {
                    type: 'object',
                    properties: {
                      format: { type: 'string' },
                      family: { type: 'string' },
                      families: {
                        type: 'array',
                        items: { type: 'string' },
                        nullable: true
                      },
                      parameter_size: { type: 'string' },
                      quantization_level: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        },
        {
          // OpenAI format
          properties: {
            object: { type: 'string' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  object: { type: 'string' },
                  created: { type: 'number' },
                  owned_by: { type: 'string' }
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getOnlineDeviceModels(@Query('format') format: string = 'ollama') {
    try {
      const models = await this.nodeService.getOnlineDeviceModels(format);

      // For this endpoint, we return the raw format without wrapping in success/data
      // to match the expected Ollama/OpenAI formats
      return models;
    } catch (error) {
      this.logger.error(`Failed to get online device models: ${error}`);
      throw new HttpException('Failed to get online device models', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('report-models')
  @ApiOperation({
    summary: 'Report device models',
    description: 'Updates the models supported by a device'
  })
  @ApiBody({
    description: 'The device models information',
    type: Object,
    schema: {
      properties: {
        device_id: {
          type: 'string',
          description: 'The ID of the device',
        },
        models: {
          type: 'array',
          description: 'List of models supported by the device',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              modified_at: { type: 'string' },
              size: { type: 'number' },
              digest: { type: 'string' },
              details: {
                type: 'object',
                properties: {
                  format: { type: 'string' },
                  family: { type: 'string' },
                  families: {
                    type: 'array',
                    items: { type: 'string' }
                  },
                  parameter_size: { type: 'string' },
                  quantization_level: { type: 'string' }
                }
              }
            }
          }
        }
      },
      required: ['device_id', 'models'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Device models updated successfully',
    schema: {
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async reportDeviceModels(@Body() request: DeviceModelReportRequest) {
    try {
      const result = await this.nodeService.reportDeviceModels(request);
      return result;
    } catch (error) {
      this.logger.error(`Failed to report device models: ${error}`);
      throw new HttpException('Failed to report device models', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
